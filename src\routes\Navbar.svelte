<!-- Navbar.svelte -->
<script>
  let searchTerm = '';
  let isMobileMenuOpen = false;
  let isSearchOpen = false;

  function toggleMobileMenu() {
    isMobileMenuOpen = !isMobileMenuOpen;
    // Close search when opening menu
    if (isMobileMenuOpen) {
      isSearchOpen = false;
    }
  }

  function toggleSearch() {
    isSearchOpen = !isSearchOpen;
    // Close mobile menu when opening search
    if (isSearchOpen) {
      isMobileMenuOpen = false;
    }
  }

  function closeMobileMenu() {
    isMobileMenuOpen = false;
  }

  function closeSearch() {
    isSearchOpen = false;
  }

  // Close mobile menu when clicking outside
  function handleClickOutside(event) {
    if (isMobileMenuOpen && !event.target.closest('.nav-container')) {
      isMobileMenuOpen = false;
    }
    if (isSearchOpen && !event.target.closest('.search-container') && !event.target.closest('.mobile-search-btn')) {
      isSearchOpen = false;
    }
  }
</script>

<svelte:window on:click={handleClickOutside} />

<nav>
  <div class="nav-container">
    <div class="nav-left">
      <!-- Logo -->
      <a href="/" class="logo" on:click={closeMobileMenu}>
        <img src="theresaderp.png" alt="Paper Surgery logo" width="32" height="32" />
        <span>Paper Surgery</span>
      </a>

      <!-- Desktop Navigation Links -->
      <ul class="nav-links desktop-only">
        <li><a href="/novels">Novels</a></li>
        <li><a href="/genres">Genres</a></li>
        <li><a href="/authors">Authors</a></li>
        <li><a href="/rankings">Rankings</a></li>
      </ul>
    </div>

    <div class="nav-right">
      <!-- Desktop Search -->
      <div class="search-container desktop-only">
        <svg xmlns="http://www.w3.org/2000/svg" width="18" height="18" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="search-icon">
          <circle cx="11" cy="11" r="8"></circle>
          <line x1="21" y1="21" x2="16.65" y2="16.65"></line>
        </svg>
        <input
          type="search"
          bind:value={searchTerm}
          placeholder="Search novels..."
        />
      </div>

      <!-- Mobile Controls -->
      <div class="mobile-controls">
        <!-- Mobile Search Button -->
        <button class="mobile-search-btn mobile-only" on:click={toggleSearch} aria-label="Toggle search">
          <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
            <circle cx="11" cy="11" r="8"></circle>
            <line x1="21" y1="21" x2="16.65" y2="16.65"></line>
          </svg>
        </button>

        <!-- Mobile Menu Button -->
        <button class="mobile-menu-btn mobile-only" on:click={toggleMobileMenu} aria-label="Toggle menu">
          <span class="hamburger {isMobileMenuOpen ? 'active' : ''}">
            <span></span>
            <span></span>
            <span></span>
          </span>
        </button>
      </div>
    </div>
  </div>

  <!-- Mobile Search Bar -->
  <div class="mobile-search {isSearchOpen ? 'active' : ''}">
    <div class="mobile-search-container">
      <svg xmlns="http://www.w3.org/2000/svg" width="18" height="18" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="search-icon">
        <circle cx="11" cy="11" r="8"></circle>
        <line x1="21" y1="21" x2="16.65" y2="16.65"></line>
      </svg>
      <input
        type="search"
        bind:value={searchTerm}
        placeholder="Search novels..."
      />
      <button class="close-search-btn" on:click={closeSearch} aria-label="Close search">
        <svg xmlns="http://www.w3.org/2000/svg" width="18" height="18" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
          <line x1="18" y1="6" x2="6" y2="18"></line>
          <line x1="6" y1="6" x2="18" y2="18"></line>
        </svg>
      </button>
    </div>
  </div>

  <!-- Mobile Menu -->
  <div class="mobile-menu {isMobileMenuOpen ? 'active' : ''}">
    <ul class="mobile-nav-links">
      <li><a href="/novels" on:click={closeMobileMenu}>Novels</a></li>
      <li><a href="/genres" on:click={closeMobileMenu}>Genres</a></li>
      <li><a href="/authors" on:click={closeMobileMenu}>Authors</a></li>
      <li><a href="/rankings" on:click={closeMobileMenu}>Rankings</a></li>
      <li><a href="/library" on:click={closeMobileMenu}>My Library</a></li>
      <li><a href="/profile" on:click={closeMobileMenu}>Profile</a></li>
    </ul>
  </div>
</nav>

<style>
  /* Variabel warna untuk tema gelap */
  :root {
    --primary-text: #F9FAFB; /* gray-50 */
    --secondary-text: #9CA3AF; /* gray-400 */
    --border-color: #374151; /* gray-700 */
    --background-color: #1F2937; /* gray-800 */
    --accent-color: #60A5FA; /* blue-400 */
  }

  nav {
    background-color: var(--background-color);
    border-bottom: 1px solid var(--border-color);
    padding: .5rem .5rem;
    font-family: 'Inter', sans-serif;
    position: sticky;
    top: 0;
    z-index: 50;
    box-shadow: 0 2px 4px rgba(0,0,0,0.5);
  }

  .nav-container {
    display: flex;
    justify-content: space-between;
    align-items: center;
    max-width: 1200px;
    margin: 0 auto;
  }

  .nav-left {
    display: flex;
    align-items: center;
    gap: 2rem;
  }

  .logo {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    font-weight: 600;
    font-size: 1.25rem;
    color: var(--primary-text);
    text-decoration: none;
  }
  
  .logo span {
      position: relative;
      top: -2px;
  }

  .nav-links {
    list-style: none;
    display: flex;
    margin: 0;
    padding: 0;
    gap: 1.5rem;
  }

  .nav-links a {
    text-decoration: none;
    color: var(--secondary-text);
    font-size: 1rem;
    padding-bottom: 0.25rem;
    position: relative;
    transition: color 0.3s ease;
  }

  .nav-links a:hover {
    color: var(--primary-text);
  }

  .nav-links a::after {
    content: '';
    position: absolute;
    width: 0;
    height: 2px;
    bottom: 0;
    left: 0;
    background-color: var(--accent-color);
    transition: width 0.3s ease;
  }

  .nav-links a:hover::after {
    width: 100%;
  }

  .nav-right {
    display: flex;
    align-items: center;
  }

  .search-container {
    position: relative;
    display: flex;
    align-items: center;
  }

  .search-icon {
    position: absolute;
    left: 0.75rem;
    color: var(--secondary-text);
    pointer-events: none; /* Memungkinkan klik menembus ikon */
  }

  input[type="search"] {
    padding: 0.5rem 0.75rem 0.5rem 2.5rem; /* Padding kiri untuk memberi ruang bagi ikon */
    border: 1px solid var(--border-color);
    border-radius: 9999px; /* Bentuk pil */
    font-size: 0.9rem;
    width: 250px;
    background-color: #374151; /* gray-700 */
    color: var(--primary-text);
    transition: all 0.3s ease;
  }

  input[type="search"]::placeholder {
    color: var(--secondary-text);
  }

  input[type="search"]:focus {
    outline: none;
    border-color: var(--accent-color);
    box-shadow: 0 0 0 3px rgba(96, 165, 250, 0.3);
    background-color: #4B5563; /* gray-600 */
  }
  
  /* Penyesuaian responsif */
  @media (max-width: 768px) {
    .nav-container {
      flex-direction: column;
      gap: 1rem;
    }
    
    .nav-left {
        width: 100%;
        justify-content: space-between;
    }

    .nav-right {
      width: 100%;
    }
    
    input[type="search"] {
      width: 100%;
    }
  }
</style>
