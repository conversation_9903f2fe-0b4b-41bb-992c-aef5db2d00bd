<script>
  import Navbar from './Navbar.svelte';
</script>

<Navbar />

<main>
  <slot />
</main>

<style>
  :global(*) {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
  }

  :global(body) {
    font-family: 'Inter', 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    line-height: 1.6;
    color: #333;
    background-color: #f8f9fa;
  }

  :global(a) {
    text-decoration: none;
    color: inherit;
  }

  :global(button) {
    border: none;
    cursor: pointer;
    font-family: inherit;
  }

  main {
    min-height: calc(100vh - 70px);
  }

  /* Ensure proper spacing for sticky navbar */
  :global(body) {
    padding-top: 0;
  }
</style>
